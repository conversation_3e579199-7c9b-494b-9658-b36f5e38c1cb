import { signUp, confirmSignUp, signIn, signOut, getCurrentUser } from '@aws-amplify/auth';

export const authService = {
    // Send OTP to phone number
    async sendOTP(phoneNumber: string) {
        try {
            // Format phone number to E.164 format
            const formattedPhone = phoneNumber.startsWith('+') ? phoneNumber : `+${phoneNumber}`;
            
            // Sign up with phone number
            const response = await signUp({
                username: formattedPhone,
                password: 'TemporaryPassword123!', // This will be changed after verification
                options: {
                    userAttributes: {
                        phone_number: formattedPhone,
                    }
                }
            });
            
            return response;
        } catch (error) {
            console.error('Error sending OTP:', error);
            throw error;
        }
    },

    // Verify OTP
    async verifyOTP(phoneNumber: string, code: string) {
        try {
            const formattedPhone = phoneNumber.startsWith('+') ? phoneNumber : `+${phoneNumber}`;
            
            // Confirm sign up with OTP
            const response = await confirmSignUp({
                username: formattedPhone,
                confirmationCode: code
            });
            
            // Sign in after successful verification
            if (response.isSignUpComplete) {
                const signInResponse = await signIn({
                    username: formattedPhone,
                    password: 'TemporaryPassword123!'
                });
                return signInResponse;
            }
            
            return response;
        } catch (error) {
            console.error('Error verifying OTP:', error);
            throw error;
        }
    },

    // Sign out
    async signOut() {
        try {
            await signOut();
        } catch (error) {
            console.error('Error signing out:', error);
            throw error;
        }
    },

    // Get current authenticated user
    async getCurrentUser() {
        try {
            return await getCurrentUser();
        } catch (error) {
            console.error('Error getting current user:', error);
            return null;
        }
    }
}; 