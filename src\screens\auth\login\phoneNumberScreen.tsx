// src/screens/PhoneNumberScreen.tsx
import React, {useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  TextInput,
  StyleSheet,
  Alert,
  ActivityIndicator,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {ArrowLeft, CaretDown} from 'phosphor-react-native';
import CountryCodeModal from '../../../components/CountryCodeModal';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamList} from '../../../../App';
import {authService} from '../../../services/authService';
import {NativeStackScreenProps} from '@react-navigation/native-stack';

type PhoneNumberScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'PhoneNumber'
>;

type Props = NativeStackScreenProps<RootStackParamList, 'PhoneNumber'>;

export default function PhoneNumberScreen({navigation}: Props) {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [showCountryModal, setShowCountryModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [selectedCountry, setSelectedCountry] = useState({
    name: 'India',
    code: '+91',
    flag: '🇮🇳',
  });

  // Function to handle phone number input with 10-digit limit
  const handlePhoneNumberChange = (text: string) => {
    // Remove any non-digit characters
    const formattedText = text.replace(/[^0-9]/g, '');

    // Limit to 10 digits
    if (formattedText.length <= 10) {
      setPhoneNumber(formattedText);
    }
  };

  const handleSendOTP = async () => {
    if (!phoneNumber) {
      Alert.alert('Error', 'Please enter a phone number');
      return;
    }

    try {
      setLoading(true);
      await authService.sendOTP(phoneNumber);
      navigation.navigate('OTPVerification', {phoneNumber});
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to send OTP');
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      {/* Back Arrow */}
      <TouchableOpacity
        onPress={() => navigation.goBack()}
        style={styles.backIcon}>
        <ArrowLeft size={24} color="#000" />
      </TouchableOpacity>

      {/* Headings */}
      <Text style={styles.heading}>Join us via phone number</Text>
      <Text style={styles.subheading}>
        We'll send you a verification code
      </Text>

      {/* Phone Input Section */}
      <View style={styles.phoneContainer}>
        <TouchableOpacity 
          style={styles.countrySelector}
          onPress={() => setShowCountryModal(true)}
        >
          <Text style={styles.flag}>{selectedCountry.flag}</Text>
          <Text style={styles.countryCode}>{selectedCountry.code}</Text>
          <CaretDown size={18} color="#000" />
        </TouchableOpacity>

        <TextInput
          style={styles.input}
          placeholder="Phone Number (e.g., +1234567890)"
          placeholderTextColor="#888"
          keyboardType="phone-pad"
          value={phoneNumber}
          onChangeText={handlePhoneNumberChange}
          maxLength={10}
          editable={!loading}
        />
      </View>

      {/* Spacer to push button to bottom */}
      <View style={styles.spacer} />

      {/* Next Button */}
      <TouchableOpacity
        style={[
          styles.nextButton,
          (phoneNumber.length < 10 || loading) && {opacity: 0.6},
        ]}
        disabled={phoneNumber.length < 10 || loading}
        onPress={handleSendOTP}>
        {loading ? (
          <ActivityIndicator color="#FFF" />
        ) : (
          <Text style={styles.nextButtonText}>Send OTP</Text>
        )}
      </TouchableOpacity>

      {/* Country Code Modal */}
      <CountryCodeModal
        visible={showCountryModal}
        onClose={() => setShowCountryModal(false)}
        onSelect={country => setSelectedCountry(country)}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFF',
    paddingHorizontal: 24,
    paddingTop: 60,
    paddingBottom: 24,
    justifyContent: 'flex-start',
  },
  backIcon: {
    marginBottom: 24,
  },
  heading: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 6,
  },
  subheading: {
    fontSize: 14,
    color: '#666',
    marginBottom: 30,
  },
  phoneContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderColor: '#CFCFCF',
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 12,
    height: 56,
    marginBottom: 24,
  },
  countrySelector: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 12,
    paddingRight: 12,
    borderRightWidth: 1,
    borderRightColor: '#CFCFCF',
  },
  flag: {
    fontSize: 24,
    marginRight: 8,
  },
  countryCode: {
    fontSize: 16,
    fontWeight: '500',
    marginRight: 4,
    color: '#000',
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: '#000',
  },
  spacer: {
    flex: 1,
  },
  nextButton: {
    backgroundColor: '#003366',
    borderRadius: 100,
    paddingVertical: 14,
    alignItems: 'center',
    marginTop: 'auto',
  },
  nextButtonText: {
    color: '#FFF',
    fontSize: 16,
    fontWeight: '600',
  },
});
