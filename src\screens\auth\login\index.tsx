// src/screens/LoginScreen.tsx
import {useNavigation} from '@react-navigation/native';
import React from 'react';
import {View, Text, TouchableOpacity, Image, StyleSheet} from 'react-native';

export default function LoginScreen() {
  const navigation = useNavigation();

  return (
    <View style={styles.container}>
      {/* Top logo */}
      <View style={styles.topSection}>
        <Image
          source={require('../../../assets/images/headerlogo.png')}
          style={styles.topLogo}
          resizeMode="contain"
        />
      </View>

      {/* Center content */}
      <View style={styles.centerSection}>
        <Image
          source={require('../../../assets/images/welcome.png')}
          style={styles.welcomeImage}
          resizeMode="contain"
        />
        <Text style={styles.title}>Welcome to Payzle</Text>
        <Text style={styles.subtitle}>Tagline goes here.</Text>
      </View>

      {/* Bottom buttons */}
      <View style={styles.bottomSection}>
        <TouchableOpacity
          style={styles.phoneButton}
          onPress={() => navigation.navigate('PhoneNumber' as never)}>
          <Text style={styles.phoneText}>Continue with Phone</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.googleButton}>
          <Image
            source={{uri: 'https://randomuser.me/api/portraits/men/32.jpg'}}
            style={styles.googleIcon}
          />
          <Text style={styles.googleText}>Continue with Google</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    justifyContent: 'space-between',
  },
  topSection: {
    alignItems: 'center',
    paddingTop: 40,
  },
  topLogo: {
    width: '100%',
    height: 40,
    marginTop: 20,
  },
  centerSection: {
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  welcomeImage: {
    width: '100%',
    height: 200,
    marginBottom: 20,
  },
  bottomSection: {
    paddingHorizontal: 32,
    paddingBottom: 40,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 6,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 20,
    textAlign: 'center',
  },
  phoneButton: {
    backgroundColor: '#003366',
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 100,
    width: '100%',
    alignItems: 'center',
    marginBottom: 12,
  },
  phoneText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 16,
  },
  googleButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ccc',
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 100,
    width: '100%',
    justifyContent: 'center',
  },
  googleIcon: {
    width: 20,
    height: 20,
    marginRight: 10,
  },
  googleText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
});
