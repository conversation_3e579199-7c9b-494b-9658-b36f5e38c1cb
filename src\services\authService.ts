import {
  signUp,
  confirmSignUp,
  signIn,
  signOut,
  getCurrentUser,
} from '@aws-amplify/auth';

export const authService = {
  // Send OTP to phone number
  async sendOTP(phoneNumber: string) {
    try {
      // Format phone number to E.164 format
      const formattedPhone = phoneNumber.startsWith('+')
        ? phoneNumber
        : `+${phoneNumber}`;

      console.log('Attempting to send OTP to:', formattedPhone);

      // Sign up with phone number
      const response = await signUp({
        username: formattedPhone,
        password: 'TemporaryPassword123!', // This will be changed after verification
        options: {
          userAttributes: {
            phone_number: formattedPhone,
          },
        },
      });

      console.log('SignUp response:', response);
      return response;
    } catch (error: any) {
      console.error('Error sending OTP:', error);
      console.error('Error details:', {
        name: error.name,
        message: error.message,
        code: error.code,
        stack: error.stack,
      });

      // Provide more user-friendly error messages
      if (error.name === 'UsernameExistsException') {
        throw new Error(
          'This phone number is already registered. Please try signing in instead.',
        );
      } else if (error.name === 'InvalidParameterException') {
        throw new Error(
          'Invalid phone number format. Please check your phone number.',
        );
      } else if (error.name === 'LimitExceededException') {
        throw new Error('Too many attempts. Please try again later.');
      }

      throw error;
    }
  },

  // Verify OTP
  async verifyOTP(phoneNumber: string, code: string) {
    try {
      const formattedPhone = phoneNumber.startsWith('+')
        ? phoneNumber
        : `+${phoneNumber}`;

      // Confirm sign up with OTP
      const response = await confirmSignUp({
        username: formattedPhone,
        confirmationCode: code,
      });

      // Sign in after successful verification
      if (response.isSignUpComplete) {
        const signInResponse = await signIn({
          username: formattedPhone,
          password: 'TemporaryPassword123!',
        });
        return signInResponse;
      }

      return response;
    } catch (error) {
      console.error('Error verifying OTP:', error);
      throw error;
    }
  },

  // Sign out
  async signOut() {
    try {
      await signOut();
    } catch (error) {
      console.error('Error signing out:', error);
      throw error;
    }
  },

  // Get current authenticated user
  async getCurrentUser() {
    try {
      return await getCurrentUser();
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  },
};
