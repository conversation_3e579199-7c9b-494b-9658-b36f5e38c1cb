# 🚨 AWS SMS Sandbox Issue - Step by Step Fix

## Problem Diagnosis
✅ **Phone number is stored in AWS Cognito** (Connection working)  
❌ **No OTP SMS received** (SMS delivery failing)

This is a classic **AWS SMS Sandbox** issue.

## Step 1: Check AWS SMS Sandbox Status

### 1.1 Go to AWS Console
1. Open [AWS Console](https://console.aws.amazon.com/)
2. Navigate to **Amazon SNS** service
3. Make sure you're in the correct region: **eu-north-1** (Stockholm)

### 1.2 Check Sandbox Status
Look for a message like:
```
🔴 This account is in the SMS sandbox in eu-north-1
```

If you see this message, you're in SMS Sandbox mode.

## Step 2: Quick Fix (For Testing)

### 2.1 Verify Your Phone Number in SNS
1. In AWS SNS Console, go to **Text messaging (SMS)**
2. Click **Sandbox destination phone numbers**
3. Click **Add phone number**
4. Enter your phone number: `+************`
5. Click **Add phone number**
6. **You'll receive an SMS** with verification code
7. Enter the verification code
8. Your number is now verified!

### 2.2 Test OTP Again
1. Open your React Native app
2. Use the **🔧 Test OTP (Debug)** button on login screen
3. Enter your verified phone number
4. Try sending OTP - **it should work now!**

## Step 3: Production Fix (For Live App)

### 3.1 Request Production Access
1. In AWS SNS Console, look for **"Request production access"**
2. Fill out the form with:
   - **Use case**: User authentication for mobile app
   - **Website URL**: Your app's website
   - **Expected monthly SMS volume**: Estimate your users
   - **Company information**: Your company details

### 3.2 Wait for Approval
- Usually takes 24-48 hours
- AWS will review your request
- Once approved, you can send SMS to any number

## Step 4: Verify IAM Permissions

### 4.1 Check Cognito IAM Role
1. Go to **AWS IAM** → **Roles**
2. Look for a role like `Cognito_*_SMS_Role`
3. Ensure it has `AmazonSNSFullAccess` or custom SMS policy

### 4.2 If Missing, Create IAM Role
```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "sns:Publish"
            ],
            "Resource": "*"
        }
    ]
}
```

## Step 5: Test with Debug Tools

### 5.1 Use the Debug Screen
1. Open app → Login → **🔧 Test OTP (Debug)**
2. Click **🔍 Diagnose SMS Issues**
3. Click **Test Amplify Config**
4. Try **Send Test OTP**
5. Check the debug logs for detailed information

## Common Error Messages

### "No error but no SMS"
- **Cause**: SMS Sandbox - number not verified
- **Fix**: Verify your number in AWS SNS

### "UsernameExistsException"
- **Cause**: Phone number already registered
- **Fix**: Use a different number or delete user from Cognito

### "LimitExceededException"
- **Cause**: Too many attempts or spending limit reached
- **Fix**: Wait or increase spending limit

## Quick Test Checklist

- [ ] AWS SNS region is **eu-north-1**
- [ ] Phone number verified in SNS sandbox
- [ ] Phone number format: `+************`
- [ ] IAM role exists for Cognito SMS
- [ ] AWS spending limit not exceeded ($1 default)

## Expected Timeline

- **Immediate**: Verify number in sandbox → OTP works
- **24-48 hours**: Production access approved → Works for all numbers
