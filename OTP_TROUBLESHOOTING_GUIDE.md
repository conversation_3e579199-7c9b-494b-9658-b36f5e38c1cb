# OTP Not Working - Troubleshooting Guide

## Issues Identified and Fixed

### 1. Phone Number Format Issue ✅ FIXED
**Problem**: Phone number was being sent without country code
**Solution**: Modified `phoneNumberScreen.tsx` to combine country code with phone number

### 2. AWS Amplify Configuration ✅ FIXED
**Problem**: Authentication flow type was set to CUSTOM_AUTH
**Solution**: Updated `aws-config.ts` to use proper verification method

### 3. Error Handling ✅ IMPROVED
**Problem**: Limited error information for debugging
**Solution**: Added comprehensive error logging and user-friendly messages

## Remaining Issues to Check

### 4. AWS SMS Sandbox (MOST LIKELY ISSUE)
Your AWS account is probably in SMS sandbox mode, which only allows sending SMS to verified phone numbers.

**To Check:**
1. Go to AWS Console → Amazon SNS
2. Look for message: "This account is in the SMS sandbox"
3. If in sandbox, you can only send SMS to verified numbers

**Solutions:**
- **For Testing**: Verify your phone number in AWS SNS console
- **For Production**: Request to move out of SMS sandbox

### 5. Missing IAM Role for SMS
AWS Cognito needs proper IAM permissions to send SMS via Amazon SNS.

**To Fix:**
1. Go to AWS Console → IAM → Roles
2. Create role for Cognito to access SNS
3. Attach policy: `AmazonSNSFullAccess` or custom SMS policy
4. Configure role in Cognito User Pool settings

### 6. AWS Region Configuration
Ensure SMS region matches your Cognito region.

## Quick Test Steps

1. **Check Console Logs**: Look for detailed error messages in React Native logs
2. **Test with Verified Number**: If in sandbox, verify your phone number in SNS first
3. **Check AWS Billing**: Ensure you have SMS spending quota set up

## Next Steps

1. Run the app and try sending OTP
2. Check React Native logs for detailed error messages
3. If still failing, check AWS Console for SMS sandbox status
4. Verify phone number in AWS SNS if in sandbox mode

## Test Phone Numbers (Sandbox Mode)
If in sandbox, you can only send to verified numbers. Add your phone number to verified list in AWS SNS console.
