// Test utility to debug OTP issues
import { authService } from '../services/authService';

export const testOTPConfiguration = async (phoneNumber: string) => {
  console.log('=== OTP Configuration Test ===');
  console.log('Phone number:', phoneNumber);
  
  try {
    console.log('Testing OTP send...');
    const result = await authService.sendOTP(phoneNumber);
    console.log('OTP send successful:', result);
    return { success: true, result };
  } catch (error: any) {
    console.error('OTP send failed:', error);
    console.error('Error details:', {
      name: error.name,
      message: error.message,
      code: error.code,
    });
    return { success: false, error };
  }
};

// Function to check AWS Amplify configuration
export const checkAmplifyConfig = () => {
  console.log('=== Amplify Configuration Check ===');
  
  try {
    const { Amplify } = require('aws-amplify');
    const config = Amplify.getConfig();
    console.log('Current Amplify config:', JSON.stringify(config, null, 2));
    return config;
  } catch (error) {
    console.error('Failed to get Amplify config:', error);
    return null;
  }
};
