// screens/home.js
import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  ScrollView,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import Layout from '../../components/Layout';
import {CaretRight} from 'phosphor-react-native';
import RepaymentCarousel from './carousel';
import ActiveCard from '../../globalComponents/activeCard';

interface ActiveCase {
  name: string;
  amountTitle: string;
  amount: string;
  date: string;
  tag: string;
  image: string;
}

const data: ActiveCase[] = [
  {
    name: '<PERSON><PERSON><PERSON> <PERSON>',
    amountTitle: 'Amount to be repaid',
    amount: '20,000',
    date: '29 Dec, 2024',
    tag: 'Today',
    image: 'https://randomuser.me/api/portraits/men/32.jpg',
  },
  {
    name: '<PERSON>oh<PERSON> <PERSON>',
    amountTitle: 'Lent Amount',
    amount: '20,000',
    date: '2nd Jan, 2025',
    tag: '',
    image: 'https://randomuser.me/api/portraits/men/35.jpg',
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    amountTitle: 'Lent Amount',
    amount: '20,000',
    date: '29 Dec, 2024',
    tag: 'Today',
    image: 'https://randomuser.me/api/portraits/men/32.jpg',
  },
  {
    name: 'Rohit Sharma',
    amountTitle: 'Lent Amount',
    amount: '20,000',
    date: '2nd Jan, 2025',
    tag: '',
    image: 'https://randomuser.me/api/portraits/men/35.jpg',
  },
  {
    name: 'Saksham Jha',
    amountTitle: 'Lent Amount',
    amount: '20,000',
    date: '29 Dec, 2024',
    tag: 'Today',
    image: 'https://randomuser.me/api/portraits/men/32.jpg',
  },
  {
    name: 'Rohit Sharma',
    amountTitle: 'Lent Amount',
    amount: '20,000',
    date: '2nd Jan, 2025',
    tag: '',
    image: 'https://randomuser.me/api/portraits/men/35.jpg',
  },
];

export default function HomeScreen() {
  const navigation = useNavigation();

  const handleNotificationPress = () => {
    navigation.navigate('Notifications' as never);
  };

  const handleProfilePress = () => {
    navigation.navigate('Account' as never);
  };

  const handleBorrowPress = () => {
    navigation.navigate('BorrowMoney' as never);
  };

  const handleRepayPress = () => {
    navigation.navigate('RepayMoney' as never);
  };

  return (
    <Layout
      title="Home"
      showNotification
      showProfile
      onNotificationPress={handleNotificationPress}
      onProfilePress={handleProfilePress}
      style={styles.layoutContainer}>
      <ScrollView style={styles.scrollView}>
        {/* Repayment Carousel */}
        <View style={styles.repaymentCarousel}>
          <RepaymentCarousel />
        </View>

        {/* Quick Actions */}
        <View style={styles.actionsContainer}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleBorrowPress}>
            <View style={styles.actionContent}>
              <Image
                source={require('../../assets/images/borrow.png')}
                style={styles.actionIcon}
                resizeMode="contain"
              />
              <Text style={styles.actionText}>Borrow Money</Text>
            </View>
            <CaretRight size={20} color="#6B7280" />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleRepayPress}>
            <View style={styles.actionContent}>
              <Image
                source={require('../../assets/images/repay.png')}
                style={styles.actionIcon}
                resizeMode="contain"
              />
              <Text style={styles.actionText}>Repay Money</Text>
            </View>
            <CaretRight size={20} color="#6B7280" />
          </TouchableOpacity>
        </View>

        {/* Active Cases */}
        <View style={styles.casesContainer}>
          <Text style={styles.sectionTitle}>Active Cases</Text>
          {data.map((item, index) => (
            <View key={index} style={styles.cardWrapper}>
              <ActiveCard
                name={item.name}
                amountTitle={item.amountTitle}
                amount={item.amount}
                dueDate={item.date}
                profileImage={item.image}
              />
            </View>
          ))}
        </View>
      </ScrollView>
    </Layout>
  );
}

const styles = StyleSheet.create({
  layoutContainer: {
    backgroundColor: '#F9FAFB',
  },
  scrollView: {
    flex: 1,
  },
  repaymentCarousel: {
    marginVertical: 16,
  },
  actionsContainer: {
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    marginHorizontal: 16,
    marginBottom: 24,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  actionContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionIcon: {
    width: 24,
    height: 24,
    marginRight: 12,
  },
  actionText: {
    fontSize: 16,
    color: '#1F2937',
    fontWeight: '500',
  },
  casesContainer: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 16,
  },
  cardWrapper: {
    marginBottom: 12,
  },
});
